"use client";

import { TableLoading } from "@/shared/components/custom/loading";
import { Table, TableBody, TableHead, TableHeader, TableRow } from "@/shared/components/shadcn/table";

const Page: React.FC = () => {
	return (
		<>
			<header className="border-border flex h-[60px] items-center justify-between border-b">
				<h1 className="text-2xl font-semibold">Testes</h1>
			</header>
			<main className="flex flex-col gap-4">
				<Table>
					<TableHeader>
						<TableRow>
							<TableHead>Coluna 1</TableHead>
							<TableHead>Coluna 2</TableHead>
							<TableHead>Coluna 3</TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						<TableLoading columns={3} />
					</TableBody>
				</Table>
			</main>
		</>
	);
};

export default Page;
