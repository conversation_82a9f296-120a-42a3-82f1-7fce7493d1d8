import { NextRequest } from "next/server";

export const runtime = "nodejs";
export const dynamic = "force-dynamic";

export async function POST(request: NextRequest) {
	try {
		const body = await request.text();
		const response = await fetch("http://192.168.155.83:10/ai/ask", {
			method: "POST",
			headers: {
				Accept: "*/*",
				Cookie: request.headers.get("Cookie") || "",
				"Content-Type": "application/json",
			},
			body,
		});

		if (!response.ok) {
			return new Response(JSON.stringify({ error: `Erro ao conectar com a API de IA ${response.statusText}` }), { status: response.status });
		}

		return new Response(response.body, {
			headers: {
				"Content-Type": response.headers.get("content-type") || "text/event-stream",
				"Cache-Control": "no-cache, no-transform",
				Connection: "keep-alive",
				"X-Accel-Buffering": "no",
			},
		});
	} catch (error) {
		console.error("Erro na API route:", error);
		return new Response(JSON.stringify({ error: "Erro interno do servidor" }), { status: 500 });
	}
}
