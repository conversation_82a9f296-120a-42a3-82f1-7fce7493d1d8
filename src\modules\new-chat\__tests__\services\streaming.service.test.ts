import { StreamErrorHandler } from "../../services/streaming/error-handler.service";
import { StreamRequestHandler } from "../../services/streaming/request-handler.service";
import { StreamProcessor } from "../../services/streaming/streaming-processor";
import { ChatStreamService } from "../../services/streaming/streaming.service";
import { IChatStreamRequest, IStreamingOptions } from "../../types/streaming.type";

jest.mock("../../services/streaming/request-handler.service");
jest.mock("../../services/streaming/streaming-processor");
jest.mock("../../services/streaming/error-handler.service");

describe("ChatStreamService", () => {
	let chatStreamService: ChatStreamService;
	let mockRequestHandler: jest.Mocked<StreamRequestHandler>;
	let mockProcessor: jest.Mocked<StreamProcessor>;
	let mockErrorHandler: jest.Mocked<StreamErrorHandler>;

	beforeEach(() => {
		mockProcessor = new StreamProcessor() as jest.Mocked<StreamProcessor>;
		mockErrorHandler = new StreamErrorHandler() as jest.Mocked<StreamErrorHandler>;
		mockRequestHandler = new StreamRequestHandler(mockProcessor, mockErrorHandler) as jest.Mocked<StreamRequestHandler>;
		mockRequestHandler.handle = jest.fn();
		chatStreamService = new ChatStreamService(mockRequestHandler);
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	describe("streamChat", () => {
		const mockRequest: IChatStreamRequest = {
			message: "Olá, IA!",
			sessionId: "session-123",
		};

		const mockOptions: IStreamingOptions = {
			onChunk: jest.fn(),
			onError: jest.fn(),
		};

		it("deve abortar stream anterior e criar novo AbortController", async () => {
			const abortSpy = jest.spyOn(AbortController.prototype, "abort");
			mockRequestHandler.handle.mockResolvedValue();

			// Primeira chamada
			await chatStreamService.streamChat(mockRequest, mockOptions);
			expect(chatStreamService.isStreaming()).toBe(true);

			// Segunda chamada deve abortar a primeira
			await chatStreamService.streamChat(mockRequest, mockOptions);
			expect(abortSpy).toHaveBeenCalled();
			expect(chatStreamService.isStreaming()).toBe(true);
		});

		it("deve chamar requestHandler.handle com parâmetros corretos", async () => {
			mockRequestHandler.handle.mockResolvedValue();

			await chatStreamService.streamChat(mockRequest, mockOptions);

			expect(mockRequestHandler.handle).toHaveBeenCalledWith(mockRequest, mockOptions, expect.any(AbortSignal));
		});

		it("deve usar signal fornecido nas options se disponível", async () => {
			const customAbortController = new AbortController();
			const optionsWithSignal: IStreamingOptions = {
				...mockOptions,
				signal: customAbortController.signal,
			};
			mockRequestHandler.handle.mockResolvedValue();

			await chatStreamService.streamChat(mockRequest, optionsWithSignal);

			expect(mockRequestHandler.handle).toHaveBeenCalledWith(mockRequest, optionsWithSignal, customAbortController.signal);
		});

		it("deve definir isStreaming como true durante o streaming", async () => {
			mockRequestHandler.handle.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));

			const streamPromise = chatStreamService.streamChat(mockRequest, mockOptions);
			expect(chatStreamService.isStreaming()).toBe(true);

			await streamPromise;
		});

		it("deve tratar erro durante o streaming", async () => {
			const error = new Error("Erro de streaming");
			mockRequestHandler.handle.mockRejectedValue(error);

			await expect(chatStreamService.streamChat(mockRequest, mockOptions)).rejects.toThrow("Erro de streaming");
			expect(chatStreamService.isStreaming()).toBe(true);
		});
	});

	describe("abortStream", () => {
		it("deve abortar AbortController ativo e definir como null", async () => {
			const abortSpy = jest.spyOn(AbortController.prototype, "abort");
			mockRequestHandler.handle.mockResolvedValue();

			await chatStreamService.streamChat({ message: "test" }, {});
			expect(chatStreamService.isStreaming()).toBe(true);

			chatStreamService.abortStream();
			expect(abortSpy).toHaveBeenCalled();
			expect(chatStreamService.isStreaming()).toBe(false);
		});

		it("não deve falhar se não há AbortController ativo", () => {
			expect(chatStreamService.isStreaming()).toBe(false);
			expect(() => chatStreamService.abortStream()).not.toThrow();
			expect(chatStreamService.isStreaming()).toBe(false);
		});
	});

	describe("isStreaming", () => {
		it("deve retornar false quando não há streaming ativo", () => {
			expect(chatStreamService.isStreaming()).toBe(false);
		});

		it("deve retornar true quando há streaming ativo", async () => {
			mockRequestHandler.handle.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));

			const streamPromise = chatStreamService.streamChat({ message: "test" }, {});
			expect(chatStreamService.isStreaming()).toBe(true);

			await streamPromise;
		});

		it("deve retornar false após abortar o streaming", async () => {
			mockRequestHandler.handle.mockResolvedValue();

			await chatStreamService.streamChat({ message: "test" }, {});
			expect(chatStreamService.isStreaming()).toBe(true);

			chatStreamService.abortStream();
			expect(chatStreamService.isStreaming()).toBe(false);
		});
	});

	describe("cleanup", () => {
		it("deve abortar stream ativo durante cleanup", async () => {
			const abortSpy = jest.spyOn(AbortController.prototype, "abort");
			mockRequestHandler.handle.mockResolvedValue();

			await chatStreamService.streamChat({ message: "test" }, {});
			expect(chatStreamService.isStreaming()).toBe(true);

			chatStreamService.cleanup();
			expect(abortSpy).toHaveBeenCalled();
			expect(chatStreamService.isStreaming()).toBe(false);
		});

		it("não deve falhar se não há stream ativo durante cleanup", () => {
			expect(chatStreamService.isStreaming()).toBe(false);
			expect(() => chatStreamService.cleanup()).not.toThrow();
			expect(chatStreamService.isStreaming()).toBe(false);
		});
	});

	describe("integração entre métodos", () => {
		it("deve permitir múltiplas operações de streaming sequenciais", async () => {
			mockRequestHandler.handle.mockResolvedValue();

			// Primeiro streaming
			await chatStreamService.streamChat({ message: "primeiro" }, {});
			expect(chatStreamService.isStreaming()).toBe(true);

			// Abortar
			chatStreamService.abortStream();
			expect(chatStreamService.isStreaming()).toBe(false);

			// Segundo streaming
			await chatStreamService.streamChat({ message: "segundo" }, {});
			expect(chatStreamService.isStreaming()).toBe(true);

			// Cleanup final
			chatStreamService.cleanup();
			expect(chatStreamService.isStreaming()).toBe(false);
		});
	});
});
