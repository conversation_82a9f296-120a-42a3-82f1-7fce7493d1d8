/* Estilos para highlight de có<PERSON> - <PERSON><PERSON> */
.hljs {
	background: transparent !important;
	color: inherit;
}

/* Tema claro para mensagens do assistente */
.markdown-content:not(.user-message) .hljs {
	color: #24292e;
}

.markdown-content:not(.user-message) .hljs-comment,
.markdown-content:not(.user-message) .hljs-quote {
	color: #6a737d;
	font-style: italic;
}

.markdown-content:not(.user-message) .hljs-keyword,
.markdown-content:not(.user-message) .hljs-selector-tag,
.markdown-content:not(.user-message) .hljs-type {
	color: #d73a49;
}

.markdown-content:not(.user-message) .hljs-string,
.markdown-content:not(.user-message) .hljs-doctag {
	color: #032f62;
}

.markdown-content:not(.user-message) .hljs-title,
.markdown-content:not(.user-message) .hljs-section,
.markdown-content:not(.user-message) .hljs-selector-id {
	color: #6f42c1;
	font-weight: bold;
}

.markdown-content:not(.user-message) .hljs-subst {
	color: #24292e;
}

.markdown-content:not(.user-message) .hljs-tag,
.markdown-content:not(.user-message) .hljs-name,
.markdown-content:not(.user-message) .hljs-attribute {
	color: #116329;
}

.markdown-content:not(.user-message) .hljs-regexp,
.markdown-content:not(.user-message) .hljs-link {
	color: #0550ae;
}

.markdown-content:not(.user-message) .hljs-symbol,
.markdown-content:not(.user-message) .hljs-bullet,
.markdown-content:not(.user-message) .hljs-built_in,
.markdown-content:not(.user-message) .hljs-builtin-name {
	color: #e36209;
}

.markdown-content:not(.user-message) .hljs-number,
.markdown-content:not(.user-message) .hljs-literal {
	color: #005cc5;
}

.markdown-content:not(.user-message) .hljs-meta {
	color: #6f42c1;
}

.markdown-content:not(.user-message) .hljs-meta-string {
	color: #032f62;
}

.markdown-content:not(.user-message) .hljs-emphasis {
	font-style: italic;
}

.markdown-content:not(.user-message) .hljs-strong {
	font-weight: bold;
}

.markdown-content:not(.user-message) .hljs-deletion {
	background: #ffeef0;
}

.markdown-content:not(.user-message) .hljs-addition {
	background: #f0fff4;
}

/* Tema escuro para modo dark */
@media (prefers-color-scheme: dark) {
	.markdown-content:not(.user-message) .hljs {
		color: #e1e4e8;
	}

	.markdown-content:not(.user-message) .hljs-comment,
	.markdown-content:not(.user-message) .hljs-quote {
		color: #8b949e;
	}

	.markdown-content:not(.user-message) .hljs-keyword,
	.markdown-content:not(.user-message) .hljs-selector-tag,
	.markdown-content:not(.user-message) .hljs-type {
		color: #ff7b72;
	}

	.markdown-content:not(.user-message) .hljs-string,
	.markdown-content:not(.user-message) .hljs-doctag {
		color: #a5d6ff;
	}

	.markdown-content:not(.user-message) .hljs-title,
	.markdown-content:not(.user-message) .hljs-section,
	.markdown-content:not(.user-message) .hljs-selector-id {
		color: #d2a8ff;
	}

	.markdown-content:not(.user-message) .hljs-tag,
	.markdown-content:not(.user-message) .hljs-name,
	.markdown-content:not(.user-message) .hljs-attribute {
		color: #7ee787;
	}

	.markdown-content:not(.user-message) .hljs-regexp,
	.markdown-content:not(.user-message) .hljs-link {
		color: #79c0ff;
	}

	.markdown-content:not(.user-message) .hljs-symbol,
	.markdown-content:not(.user-message) .hljs-bullet,
	.markdown-content:not(.user-message) .hljs-built_in,
	.markdown-content:not(.user-message) .hljs-builtin-name {
		color: #ffa657;
	}

	.markdown-content:not(.user-message) .hljs-number,
	.markdown-content:not(.user-message) .hljs-literal {
		color: #79c0ff;
	}

	.markdown-content:not(.user-message) .hljs-meta {
		color: #d2a8ff;
	}

	.markdown-content:not(.user-message) .hljs-meta-string {
		color: #a5d6ff;
	}
}

/* Estilos específicos para mensagens de usuário */
.markdown-content.user-message .hljs {
	color: rgba(255, 255, 255, 0.9);
}

.markdown-content.user-message .hljs-comment,
.markdown-content.user-message .hljs-quote {
	color: rgba(255, 255, 255, 0.6);
	font-style: italic;
}

.markdown-content.user-message .hljs-keyword,
.markdown-content.user-message .hljs-selector-tag,
.markdown-content.user-message .hljs-type {
	color: rgba(255, 200, 200, 0.9);
}

.markdown-content.user-message .hljs-string,
.markdown-content.user-message .hljs-doctag {
	color: rgba(200, 255, 200, 0.9);
}

.markdown-content.user-message .hljs-title,
.markdown-content.user-message .hljs-section,
.markdown-content.user-message .hljs-selector-id {
	color: rgba(220, 200, 255, 0.9);
	font-weight: bold;
}

.markdown-content.user-message .hljs-tag,
.markdown-content.user-message .hljs-name,
.markdown-content.user-message .hljs-attribute {
	color: rgba(255, 255, 150, 0.9);
}

.markdown-content.user-message .hljs-regexp,
.markdown-content.user-message .hljs-link {
	color: rgba(150, 200, 255, 0.9);
}

.markdown-content.user-message .hljs-symbol,
.markdown-content.user-message .hljs-bullet,
.markdown-content.user-message .hljs-built_in,
.markdown-content.user-message .hljs-builtin-name {
	color: rgba(255, 180, 100, 0.9);
}

.markdown-content.user-message .hljs-number,
.markdown-content.user-message .hljs-literal {
	color: rgba(150, 220, 255, 0.9);
}

.markdown-content.user-message .hljs-meta {
	color: rgba(220, 200, 255, 0.9);
}

.markdown-content.user-message .hljs-meta-string {
	color: rgba(200, 255, 200, 0.9);
}

/* Melhorias visuais gerais */
.markdown-content pre {
	position: relative;
}

.markdown-content pre:hover {
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Scrollbar customizada para blocos de código */
.markdown-content pre::-webkit-scrollbar {
	height: 6px;
}

.markdown-content pre::-webkit-scrollbar-track {
	background: rgba(0, 0, 0, 0.1);
	border-radius: 3px;
}

.markdown-content pre::-webkit-scrollbar-thumb {
	background: rgba(0, 0, 0, 0.3);
	border-radius: 3px;
}

.markdown-content pre::-webkit-scrollbar-thumb:hover {
	background: rgba(0, 0, 0, 0.5);
}

/* Para mensagens de usuário */
.markdown-content.user-message pre::-webkit-scrollbar-track {
	background: rgba(255, 255, 255, 0.1);
}

.markdown-content.user-message pre::-webkit-scrollbar-thumb {
	background: rgba(255, 255, 255, 0.3);
}

.markdown-content.user-message pre::-webkit-scrollbar-thumb:hover {
	background: rgba(255, 255, 255, 0.5);
}
