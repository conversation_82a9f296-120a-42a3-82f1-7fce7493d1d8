import { Check, Copy } from "lucide-react";
import { useState } from "react";
import { cn } from "../../../../shared/lib/shadcn/utils";

interface ICodeBlockProps {
	children: React.ReactNode;
	language?: string;
	className?: string;
	isUser?: boolean;
}

export const CodeBlock = ({ children, language, className, isUser = false }: ICodeBlockProps) => {
	const [copied, setCopied] = useState(false);

	const extractTextContent = (node: React.ReactNode): string => {
		if (typeof node === "string") return node;
		if (typeof node === "number") return String(node);
		if (Array.isArray(node)) return node.map(extractTextContent).join("");
		if (node && typeof node === "object" && "props" in node) {
			const reactElement = node as React.ReactElement<{ children?: React.ReactNode }>;
			return extractTextContent(reactElement.props.children);
		}
		return "";
	};

	const handleCopy = async () => {
		try {
			const textContent = extractTextContent(children);
			await navigator.clipboard.writeText(textContent);
			setCopied(true);
			setTimeout(() => setCopied(false), 2000);
		} catch (error) {
			console.error("Erro ao copiar código:", error);
		}
	};

	return (
		<div className="group relative">
			<div className="mb-2 flex items-center justify-between">
				{language && (
					<span
						className={cn(
							"rounded-md px-2 py-1 text-xs font-medium",
							isUser ? "bg-white/10 text-blue-100" : "bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400",
						)}
					>
						{language}
					</span>
				)}
				<button
					onClick={handleCopy}
					className={cn(
						"flex items-center gap-1.5 rounded-md p-1.5 text-xs opacity-0 transition-all duration-200 group-hover:opacity-100 hover:scale-105 active:scale-95",
						isUser
							? "bg-white/10 text-blue-100 hover:bg-white/20"
							: "bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700",
					)}
					title="Copiar código"
				>
					{copied ? (
						<>
							<Check className="h-3 w-3" />
							<span>Copiado!</span>
						</>
					) : (
						<>
							<Copy className="h-3 w-3" />
							<span>Copiar</span>
						</>
					)}
				</button>
			</div>
			<pre
				className={cn(
					"relative overflow-x-auto rounded-lg border p-3 font-mono text-xs",
					isUser
						? "border-white/20 bg-white/10 text-blue-50"
						: "border-gray-200 bg-gray-50 text-gray-800 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-200",
					className,
				)}
			>
				<code className={className}>{children}</code>
			</pre>
		</div>
	);
};
