import { processStreamLine } from "../../lib/process-streaming-line";

describe("processStreamLine", () => {
	it("deve retornar undefined para linhas vazias", () => {
		expect(processStreamLine("")).toBeUndefined();
		expect(processStreamLine("   ")).toBeUndefined();
	});

	it("deve ignorar linhas '[DONE]' ou começando com ':'", () => {
		expect(processStreamLine("[DONE]")).toBeUndefined();
		expect(processStreamLine(":alguma coisa")).toBeUndefined();
	});

	it("deve processar JSON válido com campo 'content'", () => {
		const result = processStreamLine('data: {"content": "abc"}');
		expect(result).toEqual({ content: "abc", type: undefined });
	});

	it("deve processar JSON válido com campo 'delta.content'", () => {
		const result = processStreamLine('data: {"delta": {"content": "xyz"}}');
		expect(result).toEqual({ content: "xyz", type: undefined });
	});

	it("deve processar JSON válido com campo 'choices[0].delta.content'", () => {
		const result = processStreamLine('data: {"choices": [{"delta": {"content": "123"}}]}');
		expect(result).toEqual({ content: "123", type: undefined });
	});

	it("deve processar JSON válido com campo 'text'", () => {
		const result = processStreamLine('data: {"text": "texto"}');
		expect(result).toEqual({ content: "texto", type: undefined });
	});

	it("deve processar JSON válido com campo 'type' e sem conteúdo", () => {
		const result = processStreamLine('data: {"type": "complete"}');
		expect(result).toEqual({ content: "", type: "complete" });
	});

	it("deve retornar undefined para JSON inválido começando com '{' ou '['", () => {
		expect(processStreamLine("data: {invalido")).toBeUndefined();
		expect(processStreamLine("data: [invalido")).toBeUndefined();
	});

	it("deve retornar objeto com content para texto não JSON", () => {
		expect(processStreamLine("data: texto livre")).toEqual({ content: "texto livre", type: undefined });
	});

	it("deve processar JSON válido sem campo de conteúdo nem type", () => {
		expect(processStreamLine("data: {}")).toBeUndefined();
	});
});
