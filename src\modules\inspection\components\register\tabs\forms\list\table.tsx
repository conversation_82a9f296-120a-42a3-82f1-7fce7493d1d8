import { useFindAllForms } from "@/modules/inspection/hooks/form/list/find-all.hook";
import { TableLoading } from "@/shared/components/custom/loading";
import { Pagination } from "@/shared/components/custom/pagination";
import { Skeleton } from "@/shared/components/shadcn/skeleton";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/shared/components/shadcn/table";
import { useIsMobile } from "@/shared/hooks/shadcn/use-mobile";
import { flexRender, getCoreRowModel, useReactTable } from "@tanstack/react-table";
import React from "react";
import { usePagination } from "../../../../../../../shared/hooks/utils";
import { FormCardMobile } from "./card-mobile";
import { inspectionFormColumns } from "./columns";

interface IFormulariosTabProps {
	searchTerm?: string;
}

export const FormTable: React.FC<IFormulariosTabProps> = ({ searchTerm }) => {
	const [rowSelection, setRowSelection] = React.useState({});
	const { currentPage, setCurrentPage, pageSize, setItemsPerPage } = usePagination();
	const isMobile = useIsMobile();

	const { data, pagination, isLoading, hasError, error } = useFindAllForms({
		page: currentPage,
		limit: pageSize,
		search: searchTerm || "",
	});

	const table = useReactTable({
		data,
		columns: inspectionFormColumns,
		state: { rowSelection },
		onRowSelectionChange: setRowSelection,
		enableRowSelection: true,
		getCoreRowModel: getCoreRowModel(),
		manualPagination: true,
		pageCount: pagination?.totalPages ?? 0,
	});

	const selectedCount = table.getFilteredSelectedRowModel().rows.length;

	const renderMobile = () => (
		<div className="flex flex-col gap-4">
			{hasError ? (
				<div className="text-center text-red-500">Erro ao carregar os dados: {error || "Ocorreu um erro desconhecido."}</div>
			) : isLoading ? (
				<TableLoading columns={inspectionFormColumns.length} />
			) : data?.length ? (
				<>
					{data.map((form, idx) => (
						<FormCardMobile key={form.id} form={form} index={idx} />
					))}
					{pagination && (
						<Pagination
							currentPage={pagination.currentPage}
							totalPages={pagination.totalPages}
							pageSize={pagination.itemsPerPage}
							totalItems={pagination.totalItems}
							selectedCount={selectedCount}
							onPageChange={setCurrentPage}
							onPageSizeChange={size => {
								setItemsPerPage(size);
								setCurrentPage(1);
							}}
							showPageSizeSelector
							showSelectedInfo
						/>
					)}
				</>
			) : (
				<div className="text-center">Nenhum resultado encontrado.</div>
			)}
		</div>
	);

	const renderDesktop = () => (
		<>
			<div className="bg-background overflow-x-auto rounded-lg border">
				<Table>
					<TableHeader className="bg-muted sticky top-0 z-10">
						{table.getHeaderGroups().map(headerGroup => (
							<TableRow key={headerGroup.id}>
								{headerGroup.headers.map(header => (
									<TableHead key={header.id} colSpan={header.colSpan} className="font-semibold whitespace-nowrap">
										{header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
									</TableHead>
								))}
							</TableRow>
						))}
					</TableHeader>
					<TableBody>
						{hasError ? (
							<TableRow>
								<TableCell colSpan={inspectionFormColumns.length} className="h-24 text-center text-red-500">
									Erro ao carregar os dados: {error || "Ocorreu um erro desconhecido."}
								</TableCell>
							</TableRow>
						) : isLoading ? (
							<TableRow>
								<TableCell colSpan={inspectionFormColumns.length} className="h-24 text-center">
									<Skeleton className="h-6 w-48" />
								</TableCell>
							</TableRow>
						) : table.getRowModel().rows.length ? (
							table.getRowModel().rows.map((row, idx) => (
								<TableRow
									key={row.id}
									data-state={row.getIsSelected() && "selected"}
									className={`${idx % 2 === 0 ? "bg-muted/20" : "bg-background"} hover:bg-primary/10 transition-colors`}
								>
									{row.getVisibleCells().map(cell => (
										<TableCell
											key={cell.id}
											className="h-[28px] max-w-[260px] overflow-hidden text-ellipsis whitespace-nowrap"
											title={String(cell.getValue() ?? "")}
										>
											{flexRender(cell.column.columnDef.cell, cell.getContext())}
										</TableCell>
									))}
								</TableRow>
							))
						) : (
							<TableRow>
								<TableCell colSpan={inspectionFormColumns.length} className="h-24 text-center">
									Nenhum resultado encontrado.
								</TableCell>
							</TableRow>
						)}
					</TableBody>
				</Table>
			</div>
			{pagination && (
				<Pagination
					currentPage={pagination.currentPage}
					totalPages={pagination.totalPages}
					pageSize={pagination.itemsPerPage}
					totalItems={pagination.totalItems}
					selectedCount={selectedCount}
					onPageChange={setCurrentPage}
					onPageSizeChange={size => {
						setItemsPerPage(size);
						setCurrentPage(1);
					}}
					showPageSizeSelector
					showSelectedInfo
				/>
			)}
		</>
	);

	return <div className="space-y-4">{isMobile ? renderMobile() : renderDesktop()}</div>;
};
