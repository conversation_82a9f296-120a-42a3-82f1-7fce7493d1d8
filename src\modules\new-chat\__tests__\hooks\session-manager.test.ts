import { renderHook } from "@testing-library/react";
import { useSessionManager } from "../../hooks/session-manager.hook";

const mockUseAtomValue = jest.fn();
const mockUseSetAtom = jest.fn();

jest.mock("jotai", () => {
	const actual = jest.requireActual("jotai");
	return {
		...actual,
		useAtomValue: (...args: unknown[]) => mockUseAtomValue(...args),
		useSetAtom: (...args: unknown[]) => mockUseSetAtom(...args),
	};
});

beforeEach(() => {
	jest.clearAllMocks();
	mockUseAtomValue.mockReset();
	mockUseSetAtom.mockReset();
});

describe("useSessionManager", () => {
	it("deve chamar createNewSession se isOpen for true e sessionId for falsy", () => {
		mockUseAtomValue.mockReturnValueOnce(undefined);
		const createNewSessionMock = jest.fn();
		const removeSessionMock = jest.fn();
		mockUseSetAtom.mockReturnValueOnce(createNewSessionMock).mockReturnValueOnce(removeSessionMock);
		renderHook(() => useSessionManager({ isOpen: true }));
		expect(createNewSessionMock).toHaveBeenCalled();
	});

	it("não deve chamar createNewSession se isOpen for false", () => {
		mockUseAtomValue.mockReturnValueOnce(undefined);
		const createNewSessionMock = jest.fn();
		const removeSessionMock = jest.fn();
		mockUseSetAtom.mockReturnValueOnce(createNewSessionMock).mockReturnValueOnce(removeSessionMock);
		renderHook(() => useSessionManager({ isOpen: false }));
		expect(createNewSessionMock).not.toHaveBeenCalled();
	});

	it("não deve chamar createNewSession se sessionId existir", () => {
		mockUseAtomValue.mockReturnValueOnce("session-1");
		const createNewSessionMock = jest.fn();
		const removeSessionMock = jest.fn();
		mockUseSetAtom.mockReturnValueOnce(createNewSessionMock).mockReturnValueOnce(removeSessionMock);
		renderHook(() => useSessionManager({ isOpen: true }));
		expect(createNewSessionMock).not.toHaveBeenCalled();
	});

	it("deve retornar createNewSession e removeSession", () => {
		mockUseAtomValue.mockReturnValueOnce("session-1");
		const createNewSessionMock = jest.fn();
		const removeSessionMock = jest.fn();
		mockUseSetAtom.mockReturnValueOnce(createNewSessionMock).mockReturnValueOnce(removeSessionMock);
		const { result } = renderHook(() => useSessionManager({ isOpen: true }));
		expect(result.current.createNewSession).toBe(createNewSessionMock);
		expect(result.current.removeSession).toBe(removeSessionMock);
	});
});
