import { handleStreamingChunk } from "../../lib/handler-chunk.lib";
import { sendStreamingMessage } from "../../lib/streaming-send-message.lib";
import { chatStreamService } from "../../services/streaming/streaming.service";

jest.mock("../../services/streaming/streaming.service");
jest.mock("../../lib/handler-chunk.lib");

describe("sendStreamingMessage", () => {
	let addMessage: jest.Mock;
	let onUpdate: jest.Mock;
	let setError: jest.Mock;
	let onStreamingStart: jest.Mock;

	beforeEach(() => {
		addMessage = jest.fn();
		onUpdate = jest.fn();
		setError = jest.fn();
		onStreamingStart = jest.fn();
		(chatStreamService.streamChat as jest.Mock).mockReset();
		(handleStreamingChunk as jest.Mock).mockReset();
	});

	it("deve adicionar mensagens do usuário e do assistente e chamar onStreamingStart", async () => {
		addMessage.mockReturnValueOnce("user-message-id").mockReturnValueOnce("assistant-message-id");

		(chatStreamService.streamChat as jest.Mock).mockImplementation(async () => {});

		await sendStreamingMessage({
			sessionId: "session-1",
			content: " Olá, IA!  ",
			addMessage,
			onUpdate,
			setError,
			onStreamingStart,
		});

		expect(addMessage).toHaveBeenCalledWith({ content: "Olá, IA!", role: "user" });
		expect(addMessage).toHaveBeenCalledWith({ content: "", role: "assistant" });
		expect(onStreamingStart).toHaveBeenCalledWith("assistant-message-id");
		expect(setError).toHaveBeenCalledWith(null);
	});

	it("deve processar chunks e chamar handleStreamingChunk acumulando conteúdo", async () => {
		addMessage.mockReturnValueOnce("user-message-id").mockReturnValueOnce("assistant-message-id");

		(chatStreamService.streamChat as jest.Mock).mockImplementation(async (_req, handlers) => {
			handlers.onChunk({ content: "Olá" });
			handlers.onChunk({ content: ", mundo!" });
		});

		await sendStreamingMessage({
			sessionId: "session-1",
			content: "Olá, IA!",
			addMessage,
			onUpdate,
			setError,
			onStreamingStart,
		});

		expect(handleStreamingChunk).toHaveBeenCalledTimes(2);
		expect(handleStreamingChunk).toHaveBeenNthCalledWith(
			1,
			expect.objectContaining({
				chunkData: { content: "Olá" },
				updateMessage: onUpdate,
				assistantMessageId: "assistant-message-id",
				currentValue: "Olá",
			}),
		);
		expect(handleStreamingChunk).toHaveBeenNthCalledWith(
			2,
			expect.objectContaining({
				chunkData: { content: ", mundo!" },
				updateMessage: onUpdate,
				assistantMessageId: "assistant-message-id",
				currentValue: "Olá, mundo!",
			}),
		);
	});

	it("deve tratar erro e atualizar mensagem com isError", async () => {
		addMessage.mockReturnValueOnce("user-message-id").mockReturnValueOnce("assistant-message-id");

		(chatStreamService.streamChat as jest.Mock).mockImplementation(async (_req, handlers) => {
			handlers.onError({ message: "Falha na IA" });
		});

		await sendStreamingMessage({
			sessionId: "session-1",
			content: "Olá, IA!",
			addMessage,
			onUpdate,
			setError,
			onStreamingStart,
		});

		expect(setError).toHaveBeenCalledWith("Falha na IA");
		expect(onUpdate).toHaveBeenCalledWith(
			"assistant-message-id",
			expect.objectContaining({
				content: "Falha na IA",
				isError: true,
			}),
		);
	});

	it("deve lançar erro se não conseguir criar mensagem do assistente", async () => {
		addMessage.mockReturnValueOnce("user-message-id").mockReturnValueOnce("");
		(chatStreamService.streamChat as jest.Mock).mockImplementation(async () => {});

		await expect(
			sendStreamingMessage({
				sessionId: "session-1",
				content: "Olá, IA!",
				addMessage,
				onUpdate,
				setError,
				onStreamingStart,
			}),
		).rejects.toThrow("Ocorreu um erro ao criar a mensagem do assistente. [CLIENT-ID-CREATOR]");
	});
});
