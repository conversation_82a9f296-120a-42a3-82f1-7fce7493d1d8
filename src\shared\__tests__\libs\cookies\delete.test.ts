import { removeCookie } from "@/shared/lib/cookies/crud/remove";
import { cookies as cookiesModule } from "next/headers";

jest.mock("next/headers", () => ({
	cookies: jest.fn(),
}));

const mockDelete = jest.fn();
const mockHas = jest.fn().mockReturnValue(true);

beforeEach(() => {
	jest.clearAllMocks();
	(cookiesModule as jest.Mock).mockResolvedValue({
		has: mockHas,
		delete: mockDelete,
	});
});

describe("deleteCookie", () => {
	it("deve deletar um cookie com sucesso", async () => {
		const { status, success, data } = await removeCookie({ name: "testCookie" });
		console.log(status, success, data);
		expect(cookiesModule).toHaveBeenCalled();
		expect(mockDelete).toHaveBeenCalledWith("testCookie");
		expect(success).toBe(true);
		expect(status).toBe(200);
	});

	it("deve falhar ao deletar um cookie", async () => {
		(cookiesModule as jest.Mock).mockRejectedValueOnce(new Error("Ocorreu um erro ao deletar o cookie"));
		const { status, success } = await removeCookie({ name: "testCookie" });
		expect(cookiesModule).toHaveBeenCalled();
		expect(mockDelete).not.toHaveBeenCalled();
		expect(success).toBe(false);
		expect(status).toBe(500);
	});
});
