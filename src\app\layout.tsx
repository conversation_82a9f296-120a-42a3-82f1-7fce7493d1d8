import { ProviderGlobal } from "@/config/providers/global";
import type { Metada<PERSON> } from "next";
import { Montserrat } from "next/font/google";
import "../layout/styles/index.css";
import "../modules/new-chat/components/markdown/markdown-styles.css";
import ErrorWrapper from "./error-wapper";

const montserrat = Montserrat({ subsets: ["latin"] });

export const metadata: Metadata = {
	title: "S.I.M³P",
	description: "Sistema Integrado de Medição e Manufatura Pormade",
	icons: { icon: "/favicon.svg" },
};

export default function RootLayout({
	children,
}: Readonly<{
	children: React.ReactNode;
}>) {
	return (
		<html lang="pt-BR">
			<body className={`${montserrat.className} bg-white antialiased`}>
				<ErrorWrapper>
					<ProviderGlobal>{children}</ProviderGlobal>
				</ErrorWrapper>
			</body>
		</html>
	);
}
