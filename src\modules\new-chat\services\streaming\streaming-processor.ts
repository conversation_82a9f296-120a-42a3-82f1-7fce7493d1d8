"use client";
import { processStreamLine } from "../../lib/process-streaming-line";
import { IStreamingOptions } from "../../types/streaming.type";

export class StreamProcessor {
	async process(body: ReadableStream<Uint8Array>, options: IStreamingOptions) {
		const reader = body.getReader();
		const decoder = new TextDecoder();
		let buffer = "";

		try {
			while (true) {
				const { done, value } = await reader.read();
				if (done) break;
				const chunk = decoder.decode(value, { stream: true });
				buffer += chunk;
				buffer = await this.processBuffer(buffer, options);
			}
			if (buffer.trim()) await this.processSingleLine(buffer.trim(), options);
		} finally {
			reader.releaseLock();
		}
	}

	private async processBuffer(buffer: string, options: IStreamingOptions): Promise<string> {
		let currentBuffer = buffer;

		while (true) {
			const newlineIndex = currentBuffer.indexOf("\n");
			if (newlineIndex !== -1) {
				const line = currentBuffer.substring(0, newlineIndex).trim();
				currentBuffer = currentBuffer.substring(newlineIndex + 1);
				if (line) await this.processSingleLine(line, options);
			} else {
				break;
			}
		}

		return currentBuffer;
	}

	private async processSingleLine(line: string, options: IStreamingOptions) {
		const result = processStreamLine(line);
		if (result) {
			if (options.onChunk) options.onChunk(result);
		} else {
			console.log("Linha não processada (sem resultado):", line);
		}
	}
}
