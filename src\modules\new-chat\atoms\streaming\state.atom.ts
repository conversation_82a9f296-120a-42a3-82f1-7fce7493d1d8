import { atom } from "jotai";

export interface IStreamingState {
	isStreaming: boolean;
	isPaused: boolean;
	currentMessageId: string | null;
	abortController: AbortController | null;
}

const initialStreamingState: IStreamingState = {
	isStreaming: false,
	isPaused: false,
	currentMessageId: null,
	abortController: null,
};

export const streamingStateAtom = atom<IStreamingState>(initialStreamingState);

export const isStreamingAtom = atom<boolean>(get => get(streamingStateAtom).isStreaming);
export const isPausedAtom = atom<boolean>(get => get(streamingStateAtom).isPaused);
export const currentStreamingMessageIdAtom = atom<string | null>(get => get(streamingStateAtom).currentMessageId);

export const startStreamingAtom = atom(null, (get, set, messageId: string) => {
	const currentState = get(streamingStateAtom);
	set(streamingStateAtom, {
		...currentState,
		isStreaming: true,
		isPaused: false,
		currentMessageId: messageId,
		abortController: new AbortController(),
	});
});

export const stopStreamingAtom = atom(null, (get, set) => {
	const currentState = get(streamingStateAtom);
	if (currentState.abortController) {
		currentState.abortController.abort();
	}
	set(streamingStateAtom, {
		...currentState,
		isStreaming: false,
		isPaused: false,
		currentMessageId: null,
		abortController: null,
	});
});

export const resetStreamingAtom = atom(null, (get, set) => {
	const currentState = get(streamingStateAtom);
	if (currentState.abortController) {
		currentState.abortController.abort();
	}
	set(streamingStateAtom, initialStreamingState);
});
