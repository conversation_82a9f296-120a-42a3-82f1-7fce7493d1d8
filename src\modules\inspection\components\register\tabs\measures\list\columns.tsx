import { IMeasuresDto } from "@/modules/inspection/hooks/measures/list/find-all.hook";
import { Badge } from "@/shared/components/shadcn/badge";
import { Checkbox } from "@radix-ui/react-checkbox";
import { ColumnDef } from "@tanstack/react-table";
import { MeasuresListActions } from "./actions";

export const columnsMeasures: ColumnDef<IMeasuresDto>[] = [
	{
		id: "select",
		header: ({ table }) => (
			<div className="flex items-center justify-start pl-2">
				<Checkbox
					checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")}
					onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}
					aria-label="Selecionar todos"
				/>
			</div>
		),
		cell: ({ row }) => (
			<div className="flex items-center justify-start pl-2">
				<Checkbox checked={row.getIsSelected()} onCheckedChange={value => row.toggleSelected(!!value)} aria-label="Selecionar linha" />
			</div>
		),
		enableSorting: false,
		enableHiding: false,
		size: 10,
	},
	{
		accessorKey: "nome",
		header: () => <div className="text-start font-semibold">Nome</div>,
		cell: ({ row }) => (
			<div className="text-start">
				<span className="text-primary block max-w-[200px] truncate font-medium">{row.original.name}</span>
			</div>
		),
	},
	{
		accessorKey: "abreviação",
		header: () => <div className="text-center font-semibold">Abreviação</div>,
		cell: ({ row }) => (
			<div className="flex justify-center">
				<Badge variant="outline" className="bg-muted-foreground/10 rounded px-3 py-1 text-xs">
					{row.original.abbreviation}
				</Badge>
			</div>
		),
	},
	{
		id: "actions",
		header: () => <div className="pr-2 text-right font-semibold">Ações</div>,
		cell: ({ row }) => <MeasuresListActions measuresId={row.original.id} name={row.original.name} />,
		enableSorting: false,
		enableHiding: false,
		size: 80,
	},
];
