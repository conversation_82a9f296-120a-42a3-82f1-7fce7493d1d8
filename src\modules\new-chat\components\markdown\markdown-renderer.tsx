import { memo } from "react";
import ReactMarkdown from "react-markdown";
import rehypeHighlight from "rehype-highlight";
import rehypeRaw from "rehype-raw";
import remarkGfm from "remark-gfm";
import { cn } from "../../../../shared/lib/shadcn/utils";
import { CodeBlock } from "./code-block";

interface IMarkdownRendererProps {
	content: string;
	className?: string;
	isUser?: boolean;
}

export const MarkdownRenderer = memo<IMarkdownRendererProps>(({ content, className, isUser = false }) => {
	return (
		<div className={cn("markdown-content", className)}>
			<ReactMarkdown
				remarkPlugins={[remarkGfm]}
				rehypePlugins={[rehypeHighlight, rehypeRaw]}
				components={{
					// Cabeçalhos
					h1: ({ children, ...props }) => (
						<h1 className="mt-4 mb-3 border-b border-current/20 pb-1 text-xl font-bold first:mt-0" {...props}>
							{children}
						</h1>
					),
					h2: ({ children, ...props }) => (
						<h2 className="mt-3 mb-2 text-lg font-semibold first:mt-0" {...props}>
							{children}
						</h2>
					),
					h3: ({ children, ...props }) => (
						<h3 className="mt-3 mb-2 text-base font-semibold first:mt-0" {...props}>
							{children}
						</h3>
					),
					h4: ({ children, ...props }) => (
						<h4 className="mt-2 mb-1 text-sm font-semibold first:mt-0" {...props}>
							{children}
						</h4>
					),
					h5: ({ children, ...props }) => (
						<h5 className="mt-2 mb-1 text-sm font-medium first:mt-0" {...props}>
							{children}
						</h5>
					),
					h6: ({ children, ...props }) => (
						<h6 className="mt-2 mb-1 text-xs font-medium opacity-80 first:mt-0" {...props}>
							{children}
						</h6>
					),

					// Parágrafos
					p: ({ children, ...props }) => (
						<p className="mb-2 leading-relaxed last:mb-0" {...props}>
							{children}
						</p>
					),

					// Links
					a: ({ children, href, ...props }) => (
						<a
							href={href}
							target="_blank"
							rel="noopener noreferrer"
							className={cn(
								"font-medium underline transition-colors hover:opacity-80",
								isUser ? "text-blue-100 hover:text-blue-200" : "text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300",
							)}
							{...props}
						>
							{children}
						</a>
					),

					// Texto forte/negrito
					strong: ({ children, ...props }) => (
						<strong className="font-semibold" {...props}>
							{children}
						</strong>
					),

					// Texto em itálico
					em: ({ children, ...props }) => (
						<em className="italic" {...props}>
							{children}
						</em>
					),

					// Código inline
					code: ({ children, className, ...props }) => {
						// Se tem className, é um bloco de código
						if (className) {
							return (
								<code className={className} {...props}>
									{children}
								</code>
							);
						}
						// Senão é código inline
						return (
							<code
								className={cn(
									"rounded px-1.5 py-0.5 font-mono text-xs",
									isUser ? "bg-white/20 text-blue-100" : "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200",
								)}
								{...props}
							>
								{children}
							</code>
						);
					},

					// Blocos de código
					pre: ({ children, ...props }) => {
						// Extrair a linguagem do código
						const child = children && Array.isArray(children) ? children[0] : children;
						let language = "";

						if (child && typeof child === "object" && "props" in child) {
							const codeElement = child as React.ReactElement<{ className?: string }>;
							const className = codeElement.props.className || "";
							const match = className.match(/language-(\w+)/);
							language = match ? match[1] : "";
						}

						return (
							<CodeBlock language={language} isUser={isUser} {...props}>
								{children}
							</CodeBlock>
						);
					},

					// Listas não ordenadas
					ul: ({ children, ...props }) => (
						<ul className="my-2 list-inside list-disc space-y-1 pl-2" {...props}>
							{children}
						</ul>
					),

					// Listas ordenadas
					ol: ({ children, ...props }) => (
						<ol className="my-2 list-inside list-decimal space-y-1 pl-2" {...props}>
							{children}
						</ol>
					),

					// Itens de lista
					li: ({ children, ...props }) => (
						<li className="leading-relaxed" {...props}>
							{children}
						</li>
					),

					// Citações
					blockquote: ({ children, ...props }) => (
						<blockquote
							className={cn(
								"my-3 border-l-4 py-2 pl-4 italic",
								isUser ? "border-white/30 bg-white/5" : "border-gray-300 bg-gray-50 dark:border-gray-600 dark:bg-gray-800/50",
							)}
							{...props}
						>
							{children}
						</blockquote>
					),

					// Divisor horizontal
					hr: ({ ...props }) => <hr className={cn("my-4 h-px border-0", isUser ? "bg-white/20" : "bg-gray-200 dark:bg-gray-700")} {...props} />,

					// Tabelas
					table: ({ children, ...props }) => (
						<div className="my-3 overflow-x-auto">
							<table
								className={cn(
									"min-w-full border-collapse text-xs",
									isUser ? "border border-white/20" : "border border-gray-200 dark:border-gray-700",
								)}
								{...props}
							>
								{children}
							</table>
						</div>
					),

					thead: ({ children, ...props }) => (
						<thead className={cn(isUser ? "bg-white/10" : "bg-gray-50 dark:bg-gray-800")} {...props}>
							{children}
						</thead>
					),

					tbody: ({ children, ...props }) => <tbody {...props}>{children}</tbody>,

					tr: ({ children, ...props }) => (
						<tr className={cn(isUser ? "border-b border-white/10" : "border-b border-gray-200 dark:border-gray-700")} {...props}>
							{children}
						</tr>
					),

					th: ({ children, ...props }) => (
						<th
							className={cn(
								"px-3 py-2 text-left font-semibold",
								isUser ? "border-r border-white/10 last:border-r-0" : "border-r border-gray-200 last:border-r-0 dark:border-gray-700",
							)}
							{...props}
						>
							{children}
						</th>
					),

					td: ({ children, ...props }) => (
						<td
							className={cn(
								"px-3 py-2",
								isUser ? "border-r border-white/10 last:border-r-0" : "border-r border-gray-200 last:border-r-0 dark:border-gray-700",
							)}
							{...props}
						>
							{children}
						</td>
					),

					// Checkbox para task lists
					input: ({ type, checked, ...props }) => {
						if (type === "checkbox") {
							return <input type="checkbox" checked={checked} readOnly className="mr-2 rounded" {...props} />;
						}
						return <input type={type} {...props} />;
					},
				}}
			>
				{content}
			</ReactMarkdown>
		</div>
	);
});

MarkdownRenderer.displayName = "MarkdownRenderer";
